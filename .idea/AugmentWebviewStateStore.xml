<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;6fabd851-34db-4b94-913f-6d02699bf5f3&quot;,&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;conversations&quot;:{&quot;6fabd851-34db-4b94-913f-6d02699bf5f3&quot;:{&quot;id&quot;:&quot;6fabd851-34db-4b94-913f-6d02699bf5f3&quot;,&quot;createdAtIso&quot;:&quot;2025-09-18T12:18:31.756Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-09-19T12:17:33.888Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;50dbd0d7-ca4c-4931-8ac3-bba8634634ad&quot;,&quot;uuid&quot;:&quot;8e08ce66-7e46-46a9-bdc1-2b41bd78be6f&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1758197911757,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;d98d1a08-6d6b-48dc-8196-f331255d9c89&quot;,&quot;timestamp&quot;:&quot;2025-09-18T12:18:54.495Z&quot;,&quot;request_message&quot;:&quot;@/client.c dosyasini go dili ile yeniden yaz. ismi client.go olsun&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;953b8f2e-e7a6-4f3e-bc35-0e1acfaec390&quot;,&quot;timestamp&quot;:&quot;2025-09-18T12:18:59.079Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;7aaa0e9e-6da1-49d4-bb0e-6060fc4f00e0&quot;,&quot;timestamp&quot;:&quot;2025-09-18T12:19:23.926Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;request_id&quot;:&quot;f2798258-41ed-47b4-9639-06c6fa4ca456&quot;,&quot;uuid&quot;:&quot;107c255a-d053-4fe4-ab43-96e8534bf363&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1758197911757,&quot;toTimestamp&quot;:1758197974218,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;request_id&quot;:&quot;79fb0ac4-b1e1-47d3-9889-7978e71a8072&quot;,&quot;uuid&quot;:&quot;2d567db6-bcf3-4af0-a92c-dd768cad648c&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1758197974218,&quot;toTimestamp&quot;:1758284253995,&quot;seen_state&quot;:&quot;unseen&quot;}],&quot;feedbackStates&quot;:{&quot;temp-fe-592785ab-8fd0-413e-90f8-ad20fe9cb11a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-6ae11dee-afc8-4c96-97a4-7d06682d3bff&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-f4305693-c2e2-46ec-8bc7-41b10b5737b3&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;46528c00-d3b1-4e1d-a538-5a1cd0c6e894&quot;}}}" />
      </map>
    </option>
  </component>
</project>