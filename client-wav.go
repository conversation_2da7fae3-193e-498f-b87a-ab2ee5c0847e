package main

import (
	"encoding/binary"
	"fmt"
	"net"
	"os"
	"unsafe"
)

const (
	TEST_FILE = "./test.wav"
)

// UDP message types - enum equivalent from C code
const (
	UDP_MSG_AUDIO_OPEN_TALK  = 20001
	UDP_MSG_AUDIO_CLOSE_TALK = 20002 // 20002
	UDP_MSG_AUDIO_TALK_FRAME = 20003 // 20003
	UDP_MSG_AUDIO_TALK_OTHER = 20004 // 20004
)

// UdpAudioMsgHead represents the packed struct from C code
// #pragma pack(1) equivalent - we'll handle packing manually
type UdpAudioMsgHead struct {
	ID       uint32    // id
	Magic    [5]byte   // magic[5]
	DataLen  uint32    // dataLen
	Data     [960]byte // data[960]
	UserName [64]byte  // userName[64]
	Password [64]byte  // password[64]
}

// toBytes converts the struct to byte slice for network transmission
// This ensures proper packing equivalent to #pragma pack(1)
func (msg *UdpAudioMsgHead) toBytes() []byte {
	size := int(unsafe.Sizeof(*msg))
	bytes := make([]byte, size)

	// Pack the struct manually to ensure proper byte order and packing
	binary.LittleEndian.PutUint32(bytes[0:4], msg.ID)
	copy(bytes[4:9], msg.Magic[:])
	binary.LittleEndian.PutUint32(bytes[9:13], msg.DataLen)
	copy(bytes[13:973], msg.Data[:])
	copy(bytes[973:1037], msg.UserName[:])
	copy(bytes[1037:1101], msg.Password[:])

	return bytes
}

func main() {
	fmt.Println("This is a UDP client")

	var sendBuff [1400]byte
	var buffer [960]byte

	// Initialize UDP address structure
	serverAddr, err := net.ResolveUDPAddr("udp", "************:49587")
	if err != nil {
		fmt.Printf("Incorrect ip address! Error: %v\n", err)
		return
	}

	// Create UDP socket
	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		fmt.Printf("Socket error: %v\n", err)
		return
	}
	defer conn.Close()

	// Open the test file
	fp, err := os.Open(TEST_FILE)
	if err != nil {
		fmt.Println("fopen err!")
		return
	}
	defer fp.Close()

	// Initialize message structure
	msg := (*UdpAudioMsgHead)(unsafe.Pointer(&sendBuff[0]))
	msg.ID = UDP_MSG_AUDIO_OPEN_TALK
	copy(msg.Magic[:], "EZLO")
	copy(msg.UserName[:], "admin")
	copy(msg.Password[:], "A0E75C21042F")

	fmt.Printf("%d,%d,%d\n", len("EZLO"), len("admin"), len("A0E75C21042F"))

	// Send initial open talk message
	msgBytes := msg.toBytes()
	ret, err := conn.Write(msgBytes)
	if err != nil {
		fmt.Printf("sendto error: %v\n", err)
		return
	}

	hasReadWavLen := 0

	// Main loop to read and send audio data
	for {
		// Clear buffer
		for i := range buffer {
			buffer[i] = 0
		}

		// Read audio data from file
		readBytes, err := fp.Read(buffer[:])
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			fmt.Println("fread error!")
			break
		}

		if readBytes < 0 {
			fmt.Println("fread error!")
			break
		}

		hasReadWavLen += readBytes

		if readBytes != 960 {
			break
		}

		// Prepare audio frame message
		msg.DataLen = uint32(unsafe.Sizeof(UdpAudioMsgHead{}))
		fmt.Printf("magic:%s,id:%d,name:%s,pwd:%s,len:%d\n",
			string(msg.Magic[:4]), msg.ID, 
			string(msg.UserName[:5]), string(msg.Password[:12]), 
			unsafe.Sizeof(UdpAudioMsgHead{}))

		// Clear data buffer and copy new audio data
		for i := range msg.Data {
			msg.Data[i] = 0
		}
		msg.ID = UDP_MSG_AUDIO_TALK_FRAME
		copy(msg.Data[:], buffer[:readBytes])

		// Send audio frame
		msgBytes = msg.toBytes()
		ret, err = conn.Write(msgBytes)
		fmt.Printf("ret:%d\n", ret)

		if err != nil {
			fmt.Printf("sendto error: %v\n", err)
			break
		}

		// Uncomment if you want to add delay between frames
		// time.Sleep(60 * time.Millisecond)
	}

	// Send close talk message
	msg.ID = UDP_MSG_AUDIO_CLOSE_TALK
	msgBytes = msg.toBytes()
	conn.Write(msgBytes)

	fmt.Printf("Total audio data read: %d bytes\n", hasReadWavLen)
}
