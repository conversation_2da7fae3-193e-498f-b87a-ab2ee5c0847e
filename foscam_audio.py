import wave
import struct

# Ham payload (sadece audio frame kısmı)
audio_hex = "58c44a814950e9c04437e1dd4d24e606"
audio_bytes = bytes.fromhex(audio_hex)

# WAV parametreleri
num_channels = 1       # Mono
sample_width = 2       # 2 byte = 16-bit
frame_rate = 8000      # 8 kHz (Foscam kameralarda yaygın)
num_frames = len(audio_bytes) // sample_width

# WAV dosyası oluştur
with wave.open("foscam_audio.wav", "wb") as wf:
    wf.setnchannels(num_channels)
    wf.setsampwidth(sample_width)
    wf.setframerate(frame_rate)
    wf.writeframes(audio_bytes)

print("WAV dosyası oluşturuldu: foscam_audio.wav")
