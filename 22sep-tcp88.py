import socket
import time
import struct

# --- Kamera bilgileri ---
CAMERA_IP = "************"
CAMERA_UDP_PORT = 10000  # Kamera hoparlör UDP portu

# --- RTP ayarları ---
SSRC = 0x12345678
payload_type = 0  # PCM μ-law = 0, A-law = 8
sequence_number = 0
timestamp = 0
sample_rate = 8000  # 8 kHz PCM

# Gönderilecek ham ses verisi (hex string)
hex_data = """
b9 00 d7 00 a1 00 1e 00 91 ff 4b ff 66 ff c5 ff 32 00 85 00 aa 00 98 00 51 00 e4 ff 79 ff
40 ff 50 ff a5 ff 1f 00 8c 00 bb 00 97 00 37 00 bb ff 59 ff 42 ff 76 ff cf ff 2d 00 7d 00
a1 00 74 00 f5 ff 66 ff 19 ff 3b ff b2 ff 34 00 87 00 8a 00 3f 00 cb ff 60 ff 1d ff 16 ff
52 ff ba ff 2e 00 84 00 9a 00 69 00 0c 00 b3 ff 86 ff 95 ff c8 ff f3 ff 0e 00 29 00 42 00
3f 00 05 00 ae ff 83 ff a1 ff e8 ff 1f 00 31 00 25 00 0c 00 f2 ff d6 ff b2 ff 8b ff 81 ff
ab ff fb ff 46 00 69 00 5b 00 2a 00 ef ff b9 ff 98 ff 8c ff 9b ff cf ff 25 00 6a 00 6a 00
15 00 a3 ff 5b ff 60 ff a9 ff ff ff 2f 00 2e 00 13 00 01 00 f4 ff d0 ff 90 ff 63 ff 7c ff
df ff 56 00 95 00 82 00 38 00 e5 ff b3 ff b4 ff d2 ff f5 ff 1f 00 58 00 93 00 9a 00 4a 00
c7 ff 69 ff 74 ff e1 ff 6d 00 cd 00 db 00 9c 00 42 00 01 00 e2 ff d2 ff cf ff f3 ff 3f 00
89 00 9e 00 83 00 51 00 1e 00 f8 ff ee ff fe ff 14 00 1d 00 28 00 3c 00 44 00 2c 00 fa ff
d1 ff ce ff e6 ff 06 00 23 00 36 00 3b 00 2d 00 17 00 ff ff dd ff b1 ff 95 ff a9 ff f4 ff
49 00 7a 00 86 00 7d 00 5a 00 15 00 c9 ff 90 ff 85 ff b5 ff 14 00 6f 00 80 00 33 00 c3 ff
88 ff 92 ff c3 ff 04 00 43 00 66 00 59 00 28 00 ef ff b5 ff 83 ff 75 ff a6 ff 0a 00 6c 00
94 00 76 00 34 00 f0 ff bd ff a0 ff 9e ff b5 ff e0 ff 1c 00 53 00 62 00 37 00 e6 ff a5 ff
9b ff c8 ff 0e 00 49 00 51 00 23 00 e6 ff ce ff cb ff d3 ff c9 ff dc ff f5 ff 26 00 3b 00
36 00 20 00 06 00 f1 ff d4 ff c5 ff bc ff bd ff cb ff f7 ff 2d 00 44 00 25 00 e8 ff b9 ff
a4 ff a7 ff ba ff e6 ff 14 00 3d 00 56 00 47 00 01 00 ac ff 7b ff 7f ff b2 ff f9 ff 3b 00
62 00 66 00 32 00 db ff 8c ff 5f ff 67 ff ac ff 1e 00 87 00 98 00 47 00 d8 ff 91 ff 8b ff
b0 ff f5 ff 44 00 7c 00 8a 00 6d 00 1f 00 b2 ff 5d ff 52 ff 98 ff 02 00 5d 00 8e 00 91 00
62 00 18 00 c9 ff 92 ff 77 ff 7b ff 99 ff e0 ff 32 00 58 00 3a 00 f3 ff b5 ff 98 ff ae ff
e5 ff 25 00
"""

# HEX -> bytes
payload_bytes = bytes.fromhex(hex_data.replace("\n", " "))

# --- UDP socket ---
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

# --- 10ms paketler için payload boyutu ---
# PCM16 8kHz mono → 160 örnek / 10ms, her örnek 2 byte → 320 byte
PACKET_SIZE = 320

start_time = time.time()
duration = 15  # saniye

try:
    while time.time() - start_time < duration and len(payload_bytes) > 0:
        try:
            # RTP header (12 byte)
            rtp_header = struct.pack("!BBHII",
                                     0x80,                 # Version 2
                                     payload_type & 0x7F,  # Payload type
                                     sequence_number,
                                     timestamp,
                                     SSRC)

            # RTP payload
            packet_payload = payload_bytes[:PACKET_SIZE]
            payload_bytes = payload_bytes[PACKET_SIZE:]

            rtp_packet = rtp_header + packet_payload

            sock.sendto(rtp_packet, (CAMERA_IP, CAMERA_UDP_PORT))

            # Sequence ve timestamp güncelle
            sequence_number = (sequence_number + 1) % 65536
            timestamp += 160  # 10ms * 8kHz = 80 örnek → PCM16 = 160 byte

            # 10ms bekle
            time.sleep(0.05)
        except Exception as e:
            print(f"Veri gönderirken hata: {e}")
finally:
    sock.close()
    print("UDP RTP gönderimi tamamlandı.")
