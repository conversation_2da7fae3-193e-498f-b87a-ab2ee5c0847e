package main

import (
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"net"
	"strings"
)

const (
	UDP_MSG_AUDIO_TALK_FRAME = 20003
)

// UdpAudioMsgHead represents the UDP audio message header
type UdpAudioMsgHead struct {
	ID       uint32    // id
	Magic    [5]byte   // magic[5]
	DataLen  uint32    // dataLen
	Data     [960]byte // data[960]
	UserName [64]byte  // userName[64]
	Password [64]byte  // password[64]
}

// toBytes converts the struct to byte slice for network transmission
func (msg *UdpAudioMsgHead) toBytes() []byte {
	bytes := make([]byte, 1101) // Total size: 4+5+4+960+64+64 = 1101

	// Pack the struct manually to ensure proper byte order and packing
	binary.LittleEndian.PutUint32(bytes[0:4], msg.ID)
	copy(bytes[4:9], msg.Magic[:])
	binary.LittleEndian.PutUint32(bytes[9:13], msg.Data<PERSON>en)
	copy(bytes[13:973], msg.Data[:])
	copy(bytes[973:1037], msg.UserName[:])
	copy(bytes[1037:1101], msg.Password[:])

	return bytes
}

func main() {
	fmt.Println("Hex packet sender - recreating captured packet")

	// Hex dump'tan alınan audio data (960 bytes)
	audioDataHex := `44db4fcc77c3c8c173c7cfd364e51ffa8b0f212390320
43c603e5c398f2d5f1cd507def283de9ece90c488c1e4c51fd1e4e13af6b60bd01f2830ce3a7f3ece3a2830d01fb60b3af6e4e11fd1e4c588c190c49ece83de5ef2d5075f1c8f2d5c3960
3e043c90322123b0f1ffa64e5cfd373c7c8c177c34fcc44db91eeec03d218c82ab037013efe3cc5344e26501
3afeffea8cd63cc945c29cc235ca2ad8d5ea00002b15d627cb35643dbb3dc43654290117f601b0ecb2d93bcb02c3ffc150c838d52ee714fc6f11bc24b13389
3c383e8d38312c9c1ae105f0dfdcc70cdfcc3a0c1a4c671d2a1e32bf8a20d7d21623170b783e1c3ae12e1c1ec6094af430e0d8cf32c580c132c5d8cf30e04af4c6091c1ee12e1c3a783e703b6231
7d21a20d2bf8a1e371d2a4c6a0c1fcc370cddfdcf0e1059c1a312c8d38383e893cb133bc246f1114fc2ee738d550c8ffc102c33bcbb2d9b0ecf6010117542
9c436bb3d643dcb35d6272b1500d5ea2ad835ca9cc245c23cc9acd6ffe80afe50134e26c534fe3c013eb037c82ad218ec0391ee44db4fcc77c3c8c173c7cfd364e51ffa8b0f212390320
43c603e5c398f2d5f1cd507def283de9ece90c488c1e4c51fd1e4e13af6b60bd01f2830ce3a7f3ece3a2830d01fb60b3af6e4e11fd1e4c588c190c49ece83de5ef2d5075f1c8f2d5c3960
3e043c90322123b0f1ffa64e5cfd373c7c8c177c34fcc44db91eeec03d218c82ab037013efe3cc5344e26501
3afeffea8cd63cc945c29cc235ca2ad8d5ea00002b15d627cb35643dbb3dc43654290117f601b0ecb2d93bcb02c3ffc150c838d52ee714fc6f11bc24b13389
3c383e8d38312c9c1ae105f0dfdcc70cdfcc3a0c1a4c671d2a1e32bf8a20d7d21623170b783e1c3ae12e1c1ec6094af430e0d8cf32c581c132c5d8cf30e04af4c6091c1ee12e1c3a783e703b6231
7d21a20d2bf8a1e371d2a4c6a0c1fcc370cddfdcf0e1059c1a312c8d38383e893cb133bc246f1114fc2ee738d550c8ffc102c33bcbb2d9b0ecf6010117542
9c436bb3d643dcb35d6272b1500d5ea2ad835ca9cc245c23cc9acd6ffe80afe50134e26c534fe3c013eb037c82ad218ec0391ee44db4fcc77c3c8c173c7cfd364e51ffa8b0f212390320
43c603e5c398f2d5f1cd507def283de9ece90c488c1e4c51fd1e4e13af6b60bd01f2830ce3a7f3ece3a2830d01fb60b3af6e4e11fd1e4c588c190c49ece83de5ef2d5075f1c8f2d5c3960
3e043c90322123b0f1ffa64e5cfd373c7c8c177c34fcc44db91eeec03d218c82ab037013efe3cc5344e26501
3afeffea8cd63cc945c29cc235ca2ad8d5ea`

	// Remove spaces and newlines from hex string
	audioDataHex = strings.ReplaceAll(audioDataHex, " ", "")
	audioDataHex = strings.ReplaceAll(audioDataHex, "\n", "")

	// Decode hex string to bytes
	audioData, err := hex.DecodeString(audioDataHex)
	if err != nil {
		fmt.Printf("Error decoding hex data: %v\n", err)
		return
	}

	// Ensure we have exactly 960 bytes
	if len(audioData) > 960 {
		audioData = audioData[:960]
	} else if len(audioData) < 960 {
		// Pad with zeros if needed
		padding := make([]byte, 960-len(audioData))
		audioData = append(audioData, padding...)
	}

	// Create UDP connection to ************:10000
	serverAddr, err := net.ResolveUDPAddr("udp", "************:10000")
	if err != nil {
		fmt.Printf("Error resolving address: %v\n", err)
		return
	}

	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		fmt.Printf("Error creating UDP connection: %v\n", err)
		return
	}
	defer conn.Close()

	// Create message structure matching the hex dump
	msg := &UdpAudioMsgHead{
		ID:      UDP_MSG_AUDIO_TALK_FRAME, // 20003
		DataLen: 1104,                     // 0x450 = 1104 bytes (from hex dump)
	}

	// Set magic, username, and password exactly as in hex dump
	copy(msg.Magic[:], "FOSC")                                      // "FOSC\0"
	copy(msg.UserName[:], "admin")                                  // "admin" + null padding
	copy(msg.Password[:], "e6500e126cd3f9c206b56c22f65d2ce2d166d282") // Password from hex dump

	// Copy audio data
	copy(msg.Data[:], audioData)

	// Send the packet
	packetBytes := msg.toBytes()
	ret, err := conn.Write(packetBytes)
	if err != nil {
		fmt.Printf("Error sending packet: %v\n", err)
		return
	}

	fmt.Printf("Packet sent successfully! Bytes sent: %d\n", ret)
	fmt.Printf("Message ID: %d\n", msg.ID)
	fmt.Printf("Magic: %s\n", string(msg.Magic[:4]))
	fmt.Printf("Data Length: %d\n", msg.DataLen)
	fmt.Printf("Username: %s\n", string(msg.UserName[:5]))
	fmt.Printf("Password: %s\n", string(msg.Password[:40]))
	fmt.Printf("Audio data length: %d bytes\n", len(audioData))
}
