from scapy.all import rdpcap, TCP
import wave
import audioop

# -----------------------------
# 1️⃣ Kullanıcı ayarları
# -----------------------------
pcap_file = "foscam-osman.pcapng"        # Wireshark dosyası
wav_file  = "foscam_audio_final.wav"   # Çıktı WAV dosyası
sample_rate = 8000                      # 8 kHz (Foscam ADPCM için)
num_channels = 1                        # Mono

# -----------------------------
# 2️⃣ PCAP dosyasını oku
# -----------------------------
packets = rdpcap(pcap_file)
adpcm_bytes = bytearray()

# -----------------------------
# 3️⃣ Foscam ADPCM paketlerini birleştir
# -----------------------------
for pkt in packets:
    if TCP in pkt:
        payload = bytes(pkt[TCP].payload)
        if len(payload) >= 8 and payload[0:4] == b'FOSC':
            # Başlıktan sonra audio frame (header 12 byte)
            adpcm_frame = payload[12:]
            adpcm_bytes.extend(adpcm_frame)

# -----------------------------
# 4️⃣ ADPCM → PCM16 decode
# -----------------------------
pcm_bytes = bytearray()
state = None  # ADPCM state = (prevsample, index)
chunk_size = 1024

for i in range(0, len(adpcm_bytes), chunk_size):
    chunk = adpcm_bytes[i:i+chunk_size]
    if state is None:
        state = (0, 0)
    decoded, state = audioop.adpcm2lin(chunk, 2, state)
    pcm_bytes.extend(decoded)

# -----------------------------
# 5️⃣ WAV dosyasını oluştur
# -----------------------------
with wave.open(wav_file, "wb") as wf:
    wf.setnchannels(num_channels)
    wf.setsampwidth(2)       # 16-bit PCM
    wf.setframerate(sample_rate)
    wf.writeframes(pcm_bytes)

print(f"WAV dosyası oluşturuldu: {wav_file}")
