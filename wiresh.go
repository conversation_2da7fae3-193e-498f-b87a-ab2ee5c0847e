package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"net"
	"os"
)

func main() {
	// Kamera IP ve Port
	camIP := "************"
	camPort := 10000

	// WAV dosyası (16-bit PCM olmalı)
	file, err := os.Open("test.wav")
	if err != nil {
		log.Fatal(err)
	}
	defer file.Close()

	// WAV header atla (44 byte)
	file.Seek(44, io.SeekStart)

	// UDP bağlantısı
	addr := &net.UDPAddr{IP: net.ParseIP(camIP), Port: camPort}
	conn, err := net.DialUDP("udp", nil, addr)
	if err != nil {
		log.Fatal(err)
	}
	defer conn.Close()

	// Wireshark'tan alınan başlangıç değerleri
	seqNum := uint16(3456)        // Wireshark'tan alınan Sequence Number
	timestamp := uint32(12345678) // Wireshark'tan alınan Timestamp
	ssrc := uint32(0x1a2b3c4d)    // Wireshark'tan alınan SSRC
	payloadType := byte(96)       // Wireshark'tan alınan Payload Type (PT)

	// Her paket için 160 örnek = 320 byte (16-bit PCM @8kHz)
	payloadSize := 320
	buffer := make([]byte, payloadSize)

	for {
		n, err := file.Read(buffer)
		if n == 0 || err == io.EOF {
			fmt.Println("Tüm paketler gönderildi.")
			break
		}

		// RTP paketi
		rtpPacket := make([]byte, 12+n)
		rtpPacket[0] = 0x80               // V=2, P=0, X=0, CC=0
		rtpPacket[1] = payloadType & 0x7F // PT, M=0
		binary.BigEndian.PutUint16(rtpPacket[2:4], seqNum)
		binary.BigEndian.PutUint32(rtpPacket[4:8], timestamp)
		binary.BigEndian.PutUint32(rtpPacket[8:12], ssrc)
		copy(rtpPacket[12:], buffer[:n])

		_, err = conn.Write(rtpPacket)
		if err != nil {
			log.Fatal(err)
		}

		// Sayaçlar
		seqNum++
		timestamp += 160 // 160 örnek @8kHz = 20ms
	}
}
