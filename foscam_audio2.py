import wave

# Ham payload (FOSC header ve session çıkarıldı)
audio_hex = (
    "58c6ce67bcccd3a094579a11e284be5eb5540b4d69cbb484f609e9b0689f4016"
    "f40bc64cd4a771d71a47d94163601f484913863c025555789c3950e6a1d0fdc8"
    "07ddbc9aa8034b75a72ac1e3f03606b53333211957fb98e2428d5a053aaabd94a9"
    "d16888f3a443bb997b7491a3ef39243e26356414c1d29f3399245b7384a491178f"
    "348f7a4ff5a83b5b4613e3df85c71680be8b6b06310f36eb4765f3075fb2"
)
audio_bytes = bytes.fromhex(audio_hex)

# WAV parametreleri
num_channels = 1       # Mono
sample_width = 2       # 16-bit
frame_rate = 8000      # 8 kHz (Foscam ADPCM / PCM)
num_frames = len(audio_bytes) // sample_width

# WAV dosyası oluştur
with wave.open("foscam_audio2.wav", "wb") as wf:
    wf.setnchannels(num_channels)
    wf.setsampwidth(sample_width)
    wf.setframerate(frame_rate)
    wf.writeframes(audio_bytes)

print("WAV dosyası oluşturuldu: foscam_audio2.wav")