package main

import (
	"encoding/binary"
	"fmt"
	"net"
	"os"
	"unsafe"
)

const (
	TEST_FILE = "./test_15s.wav"
)

// UDP message types
const (
	UDP_MSG_AUDIO_OPEN_TALK  = 20001
	UDP_MSG_AUDIO_CLOSE_TALK = 20002
	UDP_MSG_AUDIO_TALK_FRAME = 20003
	UDP_MSG_AUDIO_TALK_OTHER = 20004
)

// UdpAudioMsgHead represents the UDP audio message header
// Using packed struct equivalent in Go
type UdpAudioMsgHead struct {
	ID       uint32
	Magic    [5]byte
	DataLen  uint32
	Data     [960]byte
	UserName [64]byte
	Password [64]byte
}

// toBytes converts the struct to byte slice for network transmission
func (msg *UdpAudioMsgHead) toBytes() []byte {
	size := int(unsafe.Sizeof(*msg))
	bytes := make([]byte, size)

	// Manually pack the struct to ensure proper byte order
	binary.LittleEndian.PutUint32(bytes[0:4], msg.ID)
	copy(bytes[4:9], msg.Magic[:])
	binary.LittleEndian.PutUint32(bytes[9:13], msg.DataLen)
	copy(bytes[13:973], msg.Data[:])
	copy(bytes[973:1037], msg.UserName[:])
	copy(bytes[1037:1101], msg.Password[:])

	return bytes
}

func main() {
	fmt.Println("This is a UDP client")

	// Initialize the message structure
	msg := &UdpAudioMsgHead{
		ID:      UDP_MSG_AUDIO_OPEN_TALK,
		DataLen: uint32(unsafe.Sizeof(UdpAudioMsgHead{})),
	}

	// Set magic, username, and password
	copy(msg.Magic[:], "FOSC")
	copy(msg.UserName[:], "admin")
	copy(msg.Password[:], "E8ABFAA55A47")

	fmt.Printf("%d,%d,%d\n", len("FOSC"), len("admin"), len("E8ABFAA55A47"))

	// Open the test file
	fp, err := os.Open(TEST_FILE)
	if err != nil {
		fmt.Println("fopen err!")
		return
	}
	defer fp.Close()

	// Create UDP connection
	serverAddr, err := net.ResolveUDPAddr("udp", "************:10001")
	if err != nil {
		fmt.Printf("Error resolving address: %v\n", err)
		return
	}

	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		fmt.Printf("Error creating UDP connection: %v\n", err)
		return
	}
	defer conn.Close()

	// Send initial open talk message
	sendBytes := msg.toBytes()
	ret, err := conn.Write(sendBytes)
	if err != nil {
		fmt.Printf("Error sending open talk message: %v\n", err)
		return
	}

	hasReadWavLen := 0
	buffer := make([]byte, 960)

	// Read and send audio data in chunks
	for {
		// Clear buffer
		for i := range buffer {
			buffer[i] = 0
		}

		readBytes, err := fp.Read(buffer)
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			fmt.Printf("fread error: %v\n", err)
			break
		}

		if readBytes < 0 {
			fmt.Println("fread error!")
			break
		}

		hasReadWavLen += readBytes

		if readBytes != 960 {
			break
		}

		// Prepare audio frame message
		msg.DataLen = uint32(unsafe.Sizeof(UdpAudioMsgHead{}))
		fmt.Printf("magic:%s,id:%d,name:%s,pwd:%s,len:%d\n",
			string(msg.Magic[:4]), msg.ID, string(msg.UserName[:5]),
			string(msg.Password[:12]), unsafe.Sizeof(UdpAudioMsgHead{}))

		// Clear data buffer and copy new audio data
		for i := range msg.Data {
			msg.Data[i] = 0
		}
		msg.ID = UDP_MSG_AUDIO_TALK_FRAME
		copy(msg.Data[:], buffer[:readBytes])

		// Send audio frame
		sendBytes = msg.toBytes()
		ret, err = conn.Write(sendBytes)
		fmt.Printf("ret:%d\n", ret)

		if err != nil {
			fmt.Printf("sendto error: %v\n", err)
			break
		}

		// Uncomment if you want to add delay between frames
		// time.Sleep(60 * time.Millisecond)
	}

	// Send close talk message
	msg.ID = UDP_MSG_AUDIO_CLOSE_TALK
	sendBytes = msg.toBytes()
	conn.Write(sendBytes)

	fmt.Printf("Total audio data read: %d bytes\n", hasReadWavLen)
}
