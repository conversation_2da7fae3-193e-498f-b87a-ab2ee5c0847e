package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"net"
	"os"
	"time"
)

const (
	UDP_MSG_AUDIO_OPEN_TALK  = 20001
	UDP_MSG_AUDIO_CLOSE_TALK = 20002
	UDP_MSG_AUDIO_TALK_FRAME = 20003
	UDP_MSG_AUDIO_TALK_OTHER = 20004
)

type UdpAudioMsgHead struct {
	ID       uint32
	Magic    [5]byte
	DataLen  uint32
	Data     [960]byte
	UserName [64]byte
	Password [64]byte
}

func main() {
	addr := flag.String("addr", "************", "camera ip")
	port := flag.Int("port", 10000, "camera UDP port")
	user := flag.String("user", "admin", "username")
	pwd := flag.String("pwd", "E8ABFAA55A47", "password")
	filePath := flag.String("file", "./test_15s.wav", "test file path")
	flag.Parse()

	fmt.Println("UDP Client starting...")
	fmt.Printf("Camera: %s:%d, User: %s, File: %s\n", *addr, *port, *user, *filePath)

	file, err := os.Open(*filePath)
	if err != nil {
		fmt.Println("File open error:", err)
		return
	}
	defer file.Close()

	serverAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", *addr, *port))
	if err != nil {
		fmt.Println("ResolveUDPAddr error:", err)
		return
	}

	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		fmt.Println("DialUDP error:", err)
		return
	}
	defer conn.Close()

	var msg UdpAudioMsgHead
	msg.ID = UDP_MSG_AUDIO_OPEN_TALK
	copy(msg.Magic[:], "FOSC")
	copy(msg.UserName[:], *user)
	copy(msg.Password[:], *pwd)

	// send message
	sendPacket(conn, &msg)

	buf := make([]byte, 960)
	for {
		n, err := file.Read(buf)
		if n > 0 {
			msg.ID = UDP_MSG_AUDIO_TALK_FRAME
			msg.DataLen = uint32(n)
			copy(msg.Data[:], buf[:n])
			sendPacket(conn, &msg)
			time.Sleep(50 * time.Millisecond)
			fmt.Println("packet sent")
		}
		if err != nil {
			break
		}
	}

	msg.ID = UDP_MSG_AUDIO_CLOSE_TALK
	msg.DataLen = 0
	sendPacket(conn, &msg)

	fmt.Println("All data sent successfully")
}

func sendPacket(conn *net.UDPConn, msg *UdpAudioMsgHead) {
	buf := make([]byte, 4+5+4+960+64+64)
	offset := 0

	binary.LittleEndian.PutUint32(buf[offset:], msg.ID)
	offset += 4
	copy(buf[offset:], msg.Magic[:])
	offset += 5
	binary.LittleEndian.PutUint32(buf[offset:], msg.DataLen)
	offset += 4
	copy(buf[offset:], msg.Data[:])
	offset += 960
	copy(buf[offset:], msg.UserName[:])
	offset += 64
	copy(buf[offset:], msg.Password[:])

	conn.Write(buf)
}
