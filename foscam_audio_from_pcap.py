from scapy.all import rdpcap, TCP
import wave
import audioop

# 1️⃣ PCAP dosyasını yükle
pcap_file = "foscam-osman.pcapng"
packets = rdpcap(pcap_file)

# 2️⃣ Ham ADPCM veriyi saklayacağımız liste
adpcm_bytes = bytearray()

# 3️⃣ Foscam ADPCM paketlerini filtrele
for pkt in packets:
    if TCP in pkt:
        tcp_payload = bytes(pkt[TCP].payload)
        if len(tcp_payload) >= 8 and tcp_payload[0:4] == b'FOSC':
            # Başlıktan sonra payload'u alıyoruz (header 12 byte)
            adpcm_frame = tcp_payload[12:]
            adpcm_bytes.extend(adpcm_frame)

# 4️⃣ ADPCM → PCM16 decode
# audioop.adpcm2lin(packed, width, state)
# width = 2 (16-bit PCM output)
pcm_bytes = bytearray()
state = None  # ADPCM state: tuple (prevsample, index)
chunk_size = 1024  # Parça parça decode

for i in range(0, len(adpcm_bytes), chunk_size):
    chunk = adpcm_bytes[i:i+chunk_size]
    if state is None:
        # Başlangıç state (prevsample=0, index=0)
        state = (0, 0)
    decoded, state = audioop.adpcm2lin(chunk, 2, state)
    pcm_bytes.extend(decoded)

# 5️⃣ WAV dosyası oluştur
with wave.open("foscam_audio_adpcm_decoded.wav", "wb") as wf:
    wf.setnchannels(1)       # Mono
    wf.setsampwidth(2)       # 16-bit
    wf.setframerate(8000)    # 8 kHz
    wf.writeframes(pcm_bytes)

print("WAV dosyası oluşturuldu: foscam_audio_adpcm_decoded.wav")
