package main

import (
	"fmt"
	"net"
	"os"
	"time"
)

func main() {
	// WAV dosyasını aç
	file, err := os.Open("test.wav")
	if err != nil {
		fmt.Println("Dosya açılamadı:", err)
		return
	}
	defer file.Close()

	// Hedef UDP adresi
	serverAddr := "192.168.1.22:49587" // Wireshark'ta gördüğün adres
	udpAddr, err := net.ResolveUDPAddr("udp", serverAddr)
	if err != nil {
		fmt.Println("Adres çözülemedi:", err)
		return
	}

	// UDP bağlantısı oluştur
	conn, err := net.DialUDP("udp", nil, udpAddr)
	if err != nil {
		fmt.Println("UDP bağlantısı açılamadı:", err)
		return
	}
	defer conn.Close()

	fmt.Println("Streaming başladı...")

	buffer := make([]byte, 1024) // Her seferinde 1024 byte gönder
	for {
		n, err := file.Read(buffer)
		if err != nil {
			break
		}
		_, err = conn.Write(buffer[:n])
		if err != nil {
			fmt.Println("Gönderim hatası:", err)
			break
		}
		time.Sleep(10 * time.Millisecond) // Küçük gecikme
	}

	fmt.Println("Streaming tamamlandı.")
}
