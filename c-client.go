package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"os"
	"time"
)

const (
	UDP_MSG_AUDIO_OPEN_TALK  = 20001
	UDP_MSG_AUDIO_CLOSE_TALK = 20002
	UDP_MSG_AUDIO_TALK_FRAME = 20003
	UDP_MSG_AUDIO_TALK_OTHER = 20004
)

type UdpAudioMsgHead struct {
	ID       uint32
	Magic    [5]byte
	DataLen  uint32
	Data     [960]byte
	UserName [64]byte
	Password [64]byte
}

func main() {
	serverAddr := "192.168.1.16:10000"
	wavFile := "./test_15s.wav"
	username := "admin"
	password := "E8ABFAA55A47"

	conn, err := net.Dial("udp", serverAddr)
	if err != nil {
		panic(err)
	}
	defer conn.Close()

	file, err := os.Open(wavFile)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	// WAV header atla (44 byte)
	file.Seek(44, io.SeekStart)

	// Açılış paketi gönder
	openMsg := newMsg(UDP_MSG_AUDIO_OPEN_TALK, username, password, nil)
	sendMsg(conn, openMsg)

	// Her paket 960 byte = 480 sample (16-bit PCM) @8kHz → 60ms
	// 480 sample / 8000 sample/s = 0.06 saniye
	payloadSize := 960
	packetDuration := time.Duration(float64(payloadSize/2)/8000.0*1000) * time.Millisecond
	buffer := make([]byte, payloadSize)

	fmt.Println("Ses gönderimi başladı...")

	for {
		n, err := file.Read(buffer)
		if n == 0 || err == io.EOF {
			break
		}

		data := make([]byte, n)
		copy(data, buffer[:n])

		// Ses verisini paketle ve gönder
		audioMsg := newMsg(UDP_MSG_AUDIO_TALK_FRAME, username, password, data)
		sendMsg(conn, audioMsg)
		fmt.Println("Paket gönderildi.")

		// Gerçek zamanlı oynatma için bekle
		time.Sleep(packetDuration)
	}

	// Kapatma paketi gönder
	closeMsg := newMsg(UDP_MSG_AUDIO_CLOSE_TALK, username, password, nil)
	sendMsg(conn, closeMsg)

	fmt.Println("Ses gönderimi tamamlandı.")
}

// Paket oluşturma
func newMsg(id uint32, user, pass string, data []byte) UdpAudioMsgHead {
	var msg UdpAudioMsgHead
	msg.ID = id
	copy(msg.Magic[:], []byte("FOSC"))
	copy(msg.UserName[:], []byte(user))
	copy(msg.Password[:], []byte(pass))
	if data != nil {
		copy(msg.Data[:], data)
		msg.DataLen = uint32(len(data))
	}
	return msg
}

// UDP'ye gönderme
func sendMsg(conn net.Conn, msg UdpAudioMsgHead) {
	buf := new(bytes.Buffer)
	binary.Write(buf, binary.LittleEndian, msg)
	conn.Write(buf.Bytes())
}
