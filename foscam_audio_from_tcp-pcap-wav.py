import wave
import audioop

# -----------------------------
# 1️⃣ <PERSON><PERSON><PERSON>
# -----------------------------
raw_file = "tcp_stream_raw.bin"       # Wireshark'tan export edilen raw TCP stream
wav_file = "foscam_audio_auto2.wav"

sample_rate = 8000   # 8 kHz (Foscam ADPCM)
num_channels = 1     # Mono

# -----------------------------
# 2️⃣ Raw TCP stream dosyasını oku
# -----------------------------
with open(raw_file, "rb") as f:
    raw_bytes = f.read()

# -----------------------------
# 3️⃣ Foscam paketlerini otomatik ayıkla
# -----------------------------
adpcm_bytes = bytearray()
i = 0
while i < len(raw_bytes) - 12:
    # Foscam header kontrolü
    if raw_bytes[i:i+4] == b'FOSC':
        header_len = 12  # FOSC header + command + session
        i += header_len
        # Payload uzunluğunu header sonrası son FOSC header’a kadar al
        start = i
        while i < len(raw_bytes) - 4 and raw_bytes[i:i+4] != b'FOSC':
            i += 1
        adpcm_bytes.extend(raw_bytes[start:i])
    else:
        i += 1

# -----------------------------
# 4️⃣ ADPCM → PCM16 decode
# -----------------------------
pcm_bytes = bytearray()
state = None
chunk_size = 1024

for j in range(0, len(adpcm_bytes), chunk_size):
    chunk = adpcm_bytes[j:j+chunk_size]
    if state is None:
        state = (0, 0)
    decoded, state = audioop.adpcm2lin(chunk, 2, state)
    pcm_bytes.extend(decoded)

# -----------------------------
# 5️⃣ WAV dosyası oluştur
# -----------------------------
with wave.open(wav_file, "wb") as wf:
    wf.setnchannels(num_channels)
    wf.setsampwidth(1)
    wf.setframerate(sample_rate)
    wf.writeframes(pcm_bytes)

print(f"WAV dosyası başarıyla oluşturuldu: {wav_file}")
print(f"Toplam {len(pcm_bytes)} byte PCM verisi yazıldı.")
