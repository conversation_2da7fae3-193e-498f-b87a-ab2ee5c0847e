package main

import (
	"encoding/hex"
	"fmt"
	"net"
	"strings"
)

func main() {
	fmt.Println("Exact hex packet sender - recreating captured packet")

	// Hex dump'tan alınan tam paket (UDP payload kısmı)
	// Offset 0x2F'den itibaren (UDP header'dan sonra)
	// Message structure: ID(4) + Magic(5) + DataLen(4) + Data(960) + UserName(64) + Password(64) = 1101 bytes
	packetHex := `234e000046 4f534300 50040000` + // Header: ID=20003, Magic="FOSC\0", DataLen=1104
		`44db4fcc77c3c8c173c7cfd364e51ffa8b0f212390320` +
		`43c603e5c398f2d5f1cd507def283de9ece90c488c1e4c51fd1e4e13af6b60bd01f2830ce3a7f3ece3a2830d01fb60b3af6e4e11fd1e4c588c190c49ece83de5ef2d5075f1c8f2d5c3960` +
		`3e043c90322123b0f1ffa64e5cfd373c7c8c177c34fcc44db91eeec03d218c82ab037013efe3cc5344e26501` +
		`3afeffea8cd63cc945c29cc235ca2ad8d5ea00002b15d627cb35643dbb3dc43654290117f601b0ecb2d93bcb02c3ffc150c838d52ee714fc6f11bc24b13389` +
		`3c383e8d38312c9c1ae105f0dfdcc70cdfcc3a0c1a4c671d2a1e32bf8a20d7d21623170b783e1c3ae12e1c1ec6094af430e0d8cf32c581c132c5d8cf30e04af4c6091c1ee12e1c3a783e703b6231` +
		`7d21a20d2bf8a1e371d2a4c6a0c1fcc370cddfdcf0e1059c1a312c8d38383e893cb133bc246f1114fc2ee738d550c8ffc102c33bcbb2d9b0ecf6010117542` +
		`9c436bb3d643dcb35d6272b1500d5ea2ad835ca9cc245c23cc9acd6ffe80afe50134e26c534fe3c013eb037c82ad218ec0391ee44db4fcc77c3c8c173c7cfd364e51ffa8b0f212390320` +
		`43c603e5c398f2d5f1cd507def283de9ece90c488c1e4c51fd1e4e13af6b60bd01f2830ce3a7f3ece3a2830d01fb60b3af6e4e11fd1e4c588c190c49ece83de5ef2d5075f1c8f2d5c3960` +
		`3e043c90322123b0f1ffa64e5cfd373c7c8c177c34fcc44db91eeec03d218c82ab037013efe3cc5344e26501` +
		`3afeffea8cd63cc945c29cc235ca2ad8d5ea00002b15d627cb35643dbb3dc43654290117f601b0ecb2d93bcb02c3ffc150c838d52ee714fc6f11bc24b13389` +
		`3c383e8d38312c9c1ae105f0dfdcc70cdfcc3a0c1a4c671d2a1e32bf8a20d7d21623170b783e1c3ae12e1c1ec6094af430e0d8cf32c581c132c5d8cf30e04af4c6091c1ee12e1c3a783e703b6231` +
		`7d21a20d2bf8a1e371d2a4c6a0c1fcc370cddfdcf0e1059c1a312c8d38383e893cb133bc246f1114fc2ee738d550c8ffc102c33bcbb2d9b0ecf6010117542` +
		`9c436bb3d643dcb35d6272b1500d5ea2ad835ca9cc245c23cc9acd6ffe80afe50134e26c534fe3c013eb037c82ad218ec0391ee` + // Audio data (960 bytes)
		`61646d696e00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000` + // Username: "admin" + padding (64 bytes)
		`653635303065313236636433663963323036623536633232663635643263653264313636643238320000000000000000000000000000000000000000000000000000000000000000000000000000000000000000` // Password + padding (64 bytes)

	// Remove spaces and newlines from hex string
	packetHex = strings.ReplaceAll(packetHex, " ", "")
	packetHex = strings.ReplaceAll(packetHex, "\n", "")

	// Decode hex string to bytes
	packetData, err := hex.DecodeString(packetHex)
	if err != nil {
		fmt.Printf("Error decoding hex data: %v\n", err)
		return
	}

	fmt.Printf("Packet size: %d bytes\n", len(packetData))

	// Create UDP connection to ************:10000
	serverAddr, err := net.ResolveUDPAddr("udp", "************:10000")
	if err != nil {
		fmt.Printf("Error resolving address: %v\n", err)
		return
	}

	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		fmt.Printf("Error creating UDP connection: %v\n", err)
		return
	}
	defer conn.Close()

	// Send the exact packet
	ret, err := conn.Write(packetData)
	if err != nil {
		fmt.Printf("Error sending packet: %v\n", err)
		return
	}

	fmt.Printf("Exact packet sent successfully! Bytes sent: %d\n", ret)

	// Parse and display packet info
	if len(packetData) >= 13 {
		// Message ID (first 4 bytes, little endian)
		msgID := uint32(packetData[0]) | uint32(packetData[1])<<8 | uint32(packetData[2])<<16 | uint32(packetData[3])<<24
		fmt.Printf("Message ID: %d\n", msgID)

		// Magic (next 5 bytes)
		magic := string(packetData[4:8]) // First 4 chars of magic
		fmt.Printf("Magic: %s\n", magic)

		// Data length (next 4 bytes, little endian)
		dataLen := uint32(packetData[9]) | uint32(packetData[10])<<8 | uint32(packetData[11])<<16 | uint32(packetData[12])<<24
		fmt.Printf("Data Length: %d\n", dataLen)
	}
}
